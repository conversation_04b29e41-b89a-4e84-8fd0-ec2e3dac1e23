name: CI

on:
  pull_request:
    branches: ["*"]
  push:
    branches: ["main"]
  merge_group:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# You can leverage Vercel Remote Caching with Turbo to speed up your builds
# @link https://turborepo.com/docs/core-concepts/remote-caching#remote-caching-on-vercel-builds
env:
  FORCE_COLOR: 3
  TURBO_TEAM: ${{ vars.TURBO_TEAM }}
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Setup
        uses: ./tooling/github/setup

      - name: Copy env
        shell: bash
        run: cp .env.example .env

      - name: Lint
        run: pnpm lint && pnpm lint:ws

  format:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Setup
        uses: ./tooling/github/setup

      - name: Format
        run: pnpm format

  typecheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Setup
        uses: ./tooling/github/setup

      - name: Typecheck
        run: pnpm typecheck
