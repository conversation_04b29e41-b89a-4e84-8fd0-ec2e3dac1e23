{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"topo": {"dependsOn": ["^topo"]}, "build": {"dependsOn": ["^build"], "outputs": [".cache/tsbuildinfo.json", "dist/**"]}, "dev": {"dependsOn": ["^dev"], "cache": false, "persistent": false}, "format": {"outputs": [".cache/.prettiercache"], "outputLogs": "new-only"}, "lint": {"dependsOn": ["^topo", "^build"], "outputs": [".cache/.eslintcache"]}, "typecheck": {"dependsOn": ["^topo", "^build"], "outputs": [".cache/tsbuildinfo.json"]}, "clean": {"cache": false}, "//#clean": {"cache": false}, "push": {"cache": false, "interactive": true}, "studio": {"cache": false, "persistent": true}, "ui-add": {"cache": false, "interactive": true}}, "globalEnv": ["POSTGRES_URL", "AUTH_DISCORD_ID", "AUTH_DISCORD_SECRET", "AUTH_REDIRECT_PROXY_URL", "AUTH_SECRET", "PORT"], "globalPassThroughEnv": ["NODE_ENV", "CI", "VERCEL", "VERCEL_ENV", "VERCEL_URL", "npm_lifecycle_event"]}