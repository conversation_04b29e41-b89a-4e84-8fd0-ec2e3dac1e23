{"name": "@cindee/{{ name }}", "private": true, "type": "module", "exports": {".": "./src/index.ts"}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "devDependencies": {"@cindee/eslint-config": "workspace:*", "@cindee/prettier-config": "workspace:*", "@cindee/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cindee/prettier-config"}