{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    /** Base Options */
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ES2022",
    "lib": ["ES2022"],
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,

    /** Keep TSC performant in monorepos */
    "incremental": true,
    "disableSourceOfProjectReferenceRedirect": true,
    "tsBuildInfoFile": "${configDir}/.cache/tsbuildinfo.json",

    /** Strictness */
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "checkJs": true,

    /** Transpile using Bundler (not tsc) */
    "module": "Preserve",
    "moduleResolution": "Bundler",
    "noEmit": true
  },
  "exclude": ["node_modules", "build", "dist", ".next", ".expo"]
}
