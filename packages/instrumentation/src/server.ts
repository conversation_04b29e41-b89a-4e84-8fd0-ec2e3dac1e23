import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http'
import { resourceFromAttributes } from '@opentelemetry/resources'
import { NodeSDK } from '@opentelemetry/sdk-node'
import { BatchSpanProcessor, SimpleSpanProcessor } from '@opentelemetry/sdk-trace-node'
import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions'
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node'

const exporterOptions = {
    url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT ?? 'http://localhost:4317/v1/traces',
}

const sdk = new NodeSDK({
    resource: resourceFromAttributes({
        [ATTR_SERVICE_NAME]: 'cindee-app',
    }),
    instrumentations: [getNodeAutoInstrumentations({
        '@opentelemetry/instrumentation-fs': {
            enabled: false,
        },
        '@opentelemetry/instrumentation-net': {
            enabled: false,
        },
        '@opentelemetry/instrumentation-dns': {
            enabled: false,
        },
        '@opentelemetry/instrumentation-http': {
            enabled: true,
        },
    })],
    traceExporter: new OTLPTraceExporter(exporterOptions),
    spanProcessors: [new BatchSpanProcessor(new OTLPTraceExporter())]
})
sdk.start()