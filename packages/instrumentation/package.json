{"name": "@cindee/instrumentation", "private": true, "type": "module", "exports": {".": "./src/index.ts"}, "license": "MIT", "scripts": {"build": "tsc", "dev": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "devDependencies": {"@cindee/eslint-config": "workspace:*", "@cindee/prettier-config": "workspace:*", "@cindee/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cindee/prettier-config", "dependencies": {"@opentelemetry/auto-instrumentations-node": "^0.65.0", "@opentelemetry/exporter-trace-otlp-http": "^0.206.0", "@opentelemetry/resources": "^2.1.0", "@opentelemetry/sdk-node": "^0.206.0", "@opentelemetry/sdk-trace-node": "^2.1.0", "@opentelemetry/semantic-conventions": "^1.37.0"}}