{"name": "@cindee/auth", "private": true, "type": "module", "exports": {".": "./src/index.ts", "./middleware": "./src/middleware.ts", "./client": "./src/client.ts", "./env": "./env.ts"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "generate": "dotenv -e ../../.env -- pnpx @better-auth/cli generate --config script/auth-cli.ts --output ../db/src/auth-schema.ts", "typecheck": "tsc --noEmit"}, "dependencies": {"@better-auth/expo": "1.3.8", "@cindee/db": "workspace:*", "@t3-oss/env-nextjs": "^0.13.8", "better-auth": "1.3.8", "next": "^15.5.2", "react": "catalog:react19", "react-dom": "catalog:react19", "zod": "catalog:"}, "devDependencies": {"@better-auth/cli": "1.3.8", "@cindee/eslint-config": "workspace:*", "@cindee/prettier-config": "workspace:*", "@cindee/tsconfig": "workspace:*", "@types/react": "catalog:react19", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cindee/prettier-config"}