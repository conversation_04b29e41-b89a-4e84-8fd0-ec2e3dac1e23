{"name": "@cindee/api", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@baselime/trpc-opentelemetry-middleware": "^0.1.2", "@cindee/auth": "workspace:*", "@cindee/db": "workspace:*", "@cindee/validators": "workspace:*", "@opentelemetry/api": "^1.9.0", "@trpc/server": "catalog:", "i": "^0.3.7", "npm": "^11.6.1", "superjson": "2.2.2", "zod": "catalog:"}, "devDependencies": {"@cindee/eslint-config": "workspace:*", "@cindee/prettier-config": "workspace:*", "@cindee/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cindee/prettier-config"}